import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { ZodError } from 'zod';
import { logger } from '../utils/logger';
import { isDevelopment } from '../config';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Error response interface
interface ErrorResponse {
  success: false;
  message: string;
  code: string;
  details?: any;
  stack?: string;
  timestamp: string;
  path: string;
  method: string;
}

/**
 * Handle Prisma errors
 */
const handlePrismaError = (error: any): AppError => {
  // Check if it's a Prisma error by looking for the code property
  if (error.code) {
    switch (error.code) {
      case 'P2002':
        // Unique constraint violation
        const field = error.meta?.target as string[] | undefined;
        const fieldName = field?.[0] || 'field';
        return new AppError(
          `${fieldName} already exists`,
          409,
          'DUPLICATE_ENTRY',
          true,
          { field: fieldName }
        );

      case 'P2025':
        // Record not found
        return new AppError(
          'Record not found',
          404,
          'NOT_FOUND'
        );

      case 'P2003':
        // Foreign key constraint violation
        return new AppError(
          'Related record not found',
          400,
          'FOREIGN_KEY_VIOLATION'
        );

      case 'P2014':
        // Required relation violation
        return new AppError(
          'Required relation missing',
          400,
          'REQUIRED_RELATION_MISSING'
        );

      case 'P2021':
        // Table does not exist
        return new AppError(
          'Database table not found',
          500,
          'TABLE_NOT_FOUND'
        );

      case 'P2022':
        // Column does not exist
        return new AppError(
          'Database column not found',
          500,
          'COLUMN_NOT_FOUND'
        );

      default:
        return new AppError(
          'Database operation failed',
          500,
          'DATABASE_ERROR',
          true,
          { prismaCode: error.code }
        );
    }
  }

  return new AppError(
    'Database operation failed',
    500,
    'DATABASE_ERROR'
  );
};

/**
 * Handle Zod validation errors
 */
const handleZodError = (error: ZodError): AppError => {
  const errors = error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));

  return new AppError(
    'Validation failed',
    400,
    'VALIDATION_ERROR',
    true,
    { errors }
  );
};

/**
 * Handle JWT errors
 */
const handleJWTError = (error: Error): AppError => {
  if (error.name === 'TokenExpiredError') {
    return new AppError(
      'Token has expired',
      401,
      'TOKEN_EXPIRED'
    );
  }

  if (error.name === 'JsonWebTokenError') {
    return new AppError(
      'Invalid token',
      401,
      'TOKEN_INVALID'
    );
  }

  if (error.name === 'NotBeforeError') {
    return new AppError(
      'Token not active yet',
      401,
      'TOKEN_NOT_ACTIVE'
    );
  }

  return new AppError(
    'Authentication failed',
    401,
    'AUTH_ERROR'
  );
};

/**
 * Handle Multer errors (file upload)
 */
const handleMulterError = (error: any): AppError => {
  if (error.code === 'LIMIT_FILE_SIZE') {
    return new AppError(
      'File size too large',
      400,
      'FILE_TOO_LARGE',
      true,
      { maxSize: error.limit }
    );
  }

  if (error.code === 'LIMIT_FILE_COUNT') {
    return new AppError(
      'Too many files',
      400,
      'TOO_MANY_FILES',
      true,
      { maxCount: error.limit }
    );
  }

  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return new AppError(
      'Unexpected file field',
      400,
      'UNEXPECTED_FILE_FIELD',
      true,
      { fieldName: error.field }
    );
  }

  return new AppError(
    'File upload failed',
    400,
    'FILE_UPLOAD_ERROR'
  );
};

/**
 * Send error response
 */
const sendErrorResponse = (error: AppError, req: Request, res: Response): void => {
  const response: ErrorResponse = {
    success: false,
    message: error.message,
    code: error.code,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
  };

  // Add details in development or for operational errors
  if (isDevelopment || error.isOperational) {
    if (error.details) {
      response.details = error.details;
    }
  }

  // Add stack trace in development
  if (isDevelopment && error.stack) {
    response.stack = error.stack;
  }

  res.status(error.statusCode).json(response);
};

/**
 * Global error handling middleware
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let appError: AppError;

  // Handle known error types
  if (error instanceof AppError) {
    appError = error;
  } else if ((error as any).code && (error as any).code.startsWith('P')) {
    // Prisma error (has P-code)
    appError = handlePrismaError(error);
  } else if (error instanceof ZodError) {
    appError = handleZodError(error);
  } else if (error.name?.includes('JWT') || error.name?.includes('Token')) {
    appError = handleJWTError(error);
  } else if (error.name === 'MulterError') {
    appError = handleMulterError(error);
  } else if (error.name === 'SyntaxError' && 'body' in error) {
    appError = new AppError(
      'Invalid JSON in request body',
      400,
      'INVALID_JSON'
    );
  } else {
    // Unknown error
    appError = new AppError(
      isDevelopment ? error.message : 'Internal server error',
      500,
      'INTERNAL_ERROR',
      false
    );
  }

  // Log error
  if (!appError.isOperational || appError.statusCode >= 500) {
    logger.error('Application Error', {
      message: error.message,
      stack: error.stack,
      statusCode: appError.statusCode,
      code: appError.code,
      path: req.path,
      method: req.method,
      userId: req.user?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
  } else {
    logger.warn('Operational Error', {
      message: error.message,
      statusCode: appError.statusCode,
      code: appError.code,
      path: req.path,
      method: req.method,
      userId: req.user?.id,
    });
  }

  sendErrorResponse(appError, req, res);
};

/**
 * Handle 404 errors (route not found)
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new AppError(
    `Route ${req.method} ${req.path} not found`,
    404,
    'ROUTE_NOT_FOUND'
  );

  next(error);
};

/**
 * Async error wrapper - catches async errors and passes them to error handler
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Create error helper functions
 */
export const createError = {
  badRequest: (message: string, code: string = 'BAD_REQUEST', details?: any) =>
    new AppError(message, 400, code, true, details),

  unauthorized: (message: string = 'Unauthorized', code: string = 'UNAUTHORIZED') =>
    new AppError(message, 401, code),

  forbidden: (message: string = 'Forbidden', code: string = 'FORBIDDEN') =>
    new AppError(message, 403, code),

  notFound: (message: string = 'Not found', code: string = 'NOT_FOUND') =>
    new AppError(message, 404, code),

  conflict: (message: string, code: string = 'CONFLICT', details?: any) =>
    new AppError(message, 409, code, true, details),

  unprocessableEntity: (message: string, code: string = 'UNPROCESSABLE_ENTITY', details?: any) =>
    new AppError(message, 422, code, true, details),

  tooManyRequests: (message: string = 'Too many requests', code: string = 'TOO_MANY_REQUESTS') =>
    new AppError(message, 429, code),

  internal: (message: string = 'Internal server error', code: string = 'INTERNAL_ERROR') =>
    new AppError(message, 500, code, false),

  serviceUnavailable: (message: string = 'Service unavailable', code: string = 'SERVICE_UNAVAILABLE') =>
    new AppError(message, 503, code),
};

export default errorHandler;
