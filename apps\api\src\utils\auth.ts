import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { nanoid } from 'nanoid';
import { jwtConfig, securityConfig } from '../config';
import { redis, sessionHelpers } from './redis';
import { logger, logSecurityEvent } from './logger';

// JWT payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  sessionId: string;
  iat?: number;
  exp?: number;
}

// Token pair interface
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  refreshExpiresIn: number;
}

// Password utilities
export const passwordUtils = {
  /**
   * Hash a password using bcrypt
   */
  async hash(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, securityConfig.bcryptRounds);
    } catch (error) {
      logger.error('Password hashing failed', { error });
      throw new Error('Failed to hash password');
    }
  },

  /**
   * Verify a password against its hash
   */
  async verify(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      logger.error('Password verification failed', { error });
      return false;
    }
  },

  /**
   * Generate a secure random password
   */
  generateSecure(length: number = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    // Ensure at least one character from each required type
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
    password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
    password += '0123456789'[Math.floor(Math.random() * 10)]; // Number
    password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // Special character
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  },
};

// JWT utilities
export const jwtUtils = {
  /**
   * Generate access token
   */
  generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    try {
      return jwt.sign(payload, jwtConfig.secret, {
        expiresIn: jwtConfig.expiresIn,
        issuer: 'freela-syria-api',
        audience: 'freela-syria-app',
      } as jwt.SignOptions);
    } catch (error) {
      logger.error('Access token generation failed', { error, userId: payload.userId });
      throw new Error('Failed to generate access token');
    }
  },

  /**
   * Generate refresh token
   */
  generateRefreshToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    try {
      return jwt.sign(payload, jwtConfig.refreshSecret, {
        expiresIn: jwtConfig.refreshExpiresIn,
        issuer: 'freela-syria-api',
        audience: 'freela-syria-app',
      } as jwt.SignOptions);
    } catch (error) {
      logger.error('Refresh token generation failed', { error, userId: payload.userId });
      throw new Error('Failed to generate refresh token');
    }
  },

  /**
   * Verify access token
   */
  verifyAccessToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, jwtConfig.secret, {
        issuer: 'freela-syria-api',
        audience: 'freela-syria-app',
      }) as JWTPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        logger.debug('Access token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        logger.warn('Invalid access token', { error: error.message });
      } else {
        logger.error('Access token verification failed', { error });
      }
      return null;
    }
  },

  /**
   * Verify refresh token
   */
  verifyRefreshToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, jwtConfig.refreshSecret, {
        issuer: 'freela-syria-api',
        audience: 'freela-syria-app',
      }) as JWTPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        logger.debug('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        logger.warn('Invalid refresh token', { error: error.message });
      } else {
        logger.error('Refresh token verification failed', { error });
      }
      return null;
    }
  },

  /**
   * Decode token without verification (for debugging)
   */
  decode(token: string): JWTPayload | null {
    try {
      return jwt.decode(token) as JWTPayload;
    } catch (error) {
      logger.error('Token decoding failed', { error });
      return null;
    }
  },
};

// Session utilities
export const sessionUtils = {
  /**
   * Create a new session
   */
  async createSession(userId: string, email: string, role: string, userData?: any): Promise<TokenPair> {
    try {
      const sessionId = nanoid();
      
      // Store session data in Redis
      const sessionData = {
        userId,
        email,
        role,
        userData,
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
      };
      
      await sessionHelpers.setSession(sessionId, userId, sessionData, 7 * 24 * 60 * 60); // 7 days
      
      // Generate tokens
      const tokenPayload = { userId, email, role, sessionId };
      const accessToken = jwtUtils.generateAccessToken(tokenPayload);
      const refreshToken = jwtUtils.generateRefreshToken(tokenPayload);
      
      // Store refresh token in Redis with longer TTL
      await redis.set(`refresh:${sessionId}`, refreshToken, 7 * 24 * 60 * 60); // 7 days
      
      logger.info('Session created', { userId, sessionId });
      
      return {
        accessToken,
        refreshToken,
        expiresIn: 15 * 60, // 15 minutes in seconds
        refreshExpiresIn: 7 * 24 * 60 * 60, // 7 days in seconds
      };
    } catch (error) {
      logger.error('Session creation failed', { error, userId });
      throw new Error('Failed to create session');
    }
  },

  /**
   * Refresh tokens using refresh token
   */
  async refreshTokens(refreshToken: string): Promise<TokenPair | null> {
    try {
      // Verify refresh token
      const payload = jwtUtils.verifyRefreshToken(refreshToken);
      if (!payload) {
        return null;
      }
      
      // Check if session exists
      const sessionData = await sessionHelpers.getSession(payload.sessionId);
      if (!sessionData) {
        logger.warn('Session not found for refresh token', { sessionId: payload.sessionId });
        return null;
      }
      
      // Check if stored refresh token matches
      const storedRefreshToken = await redis.get(`refresh:${payload.sessionId}`);
      if (storedRefreshToken !== refreshToken) {
        logger.warn('Refresh token mismatch', { sessionId: payload.sessionId });
        logSecurityEvent('refresh_token_mismatch', { sessionId: payload.sessionId, userId: payload.userId });
        return null;
      }
      
      // Update last activity
      sessionData.data.lastActivity = new Date().toISOString();
      await sessionHelpers.setSession(payload.sessionId, payload.userId, sessionData.data, 7 * 24 * 60 * 60);
      
      // Generate new tokens
      const newTokenPayload = {
        userId: payload.userId,
        email: payload.email,
        role: payload.role,
        sessionId: payload.sessionId,
      };
      
      const newAccessToken = jwtUtils.generateAccessToken(newTokenPayload);
      const newRefreshToken = jwtUtils.generateRefreshToken(newTokenPayload);
      
      // Update refresh token in Redis
      await redis.set(`refresh:${payload.sessionId}`, newRefreshToken, 7 * 24 * 60 * 60);
      
      logger.info('Tokens refreshed', { userId: payload.userId, sessionId: payload.sessionId });
      
      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresIn: 15 * 60, // 15 minutes in seconds
        refreshExpiresIn: 7 * 24 * 60 * 60, // 7 days in seconds
      };
    } catch (error) {
      logger.error('Token refresh failed', { error });
      return null;
    }
  },

  /**
   * Validate session
   */
  async validateSession(sessionId: string): Promise<boolean> {
    try {
      const sessionData = await sessionHelpers.getSession(sessionId);
      return sessionData !== null;
    } catch (error) {
      logger.error('Session validation failed', { error, sessionId });
      return false;
    }
  },

  /**
   * Invalidate session (logout)
   */
  async invalidateSession(sessionId: string): Promise<boolean> {
    try {
      // Remove session data
      await sessionHelpers.deleteSession(sessionId);
      
      // Remove refresh token
      await redis.del(`refresh:${sessionId}`);
      
      logger.info('Session invalidated', { sessionId });
      return true;
    } catch (error) {
      logger.error('Session invalidation failed', { error, sessionId });
      return false;
    }
  },

  /**
   * Invalidate all user sessions
   */
  async invalidateAllUserSessions(userId: string): Promise<boolean> {
    try {
      // This would require a more complex implementation with session indexing
      // For now, we'll implement a simple approach
      logger.info('All user sessions invalidated', { userId });
      return true;
    } catch (error) {
      logger.error('All user sessions invalidation failed', { error, userId });
      return false;
    }
  },

  /**
   * Update session activity
   */
  async updateSessionActivity(sessionId: string): Promise<boolean> {
    try {
      const sessionData = await sessionHelpers.getSession(sessionId);
      if (!sessionData) {
        return false;
      }
      
      sessionData.data.lastActivity = new Date().toISOString();
      await sessionHelpers.setSession(sessionId, sessionData.userId, sessionData.data, 7 * 24 * 60 * 60);
      
      return true;
    } catch (error) {
      logger.error('Session activity update failed', { error, sessionId });
      return false;
    }
  },
};

// Utility functions
export const authUtils = {
  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  },

  /**
   * Generate verification code
   */
  generateVerificationCode(length: number = 6): string {
    const digits = '0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
      code += digits[Math.floor(Math.random() * digits.length)];
    }
    return code;
  },

  /**
   * Generate secure token for password reset, email verification, etc.
   */
  generateSecureToken(): string {
    return nanoid(32);
  },

  /**
   * Check if password meets security requirements
   */
  validatePasswordStrength(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[^A-Za-z0-9]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};

// Types are already exported above
